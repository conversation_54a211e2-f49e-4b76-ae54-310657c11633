<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->string('booking_reference')->unique();
            $table->string('attendee_name');
            $table->string('attendee_email');
            $table->string('attendee_phone');
            $table->enum('pass_type', ['regular', 'vip', 'premium', 'couple', 'family'])->default('regular');
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 8, 2);
            $table->decimal('total_amount', 8, 2);
            $table->enum('payment_method', ['stripe', 'paypal', 'cash', 'bank_transfer'])->default('stripe');
            $table->string('payment_reference')->nullable();
            $table->enum('payment_status', ['pending', 'completed', 'failed', 'refunded'])->default('pending');
            $table->enum('booking_status', ['confirmed', 'cancelled', 'attended'])->default('confirmed');
            $table->timestamp('payment_completed_at')->nullable();
            $table->text('special_requirements')->nullable();
            $table->json('additional_info')->nullable(); // Store any additional booking info
            $table->timestamps();

            $table->index(['user_id', 'event_id']);
            $table->index('booking_reference');
            $table->index('payment_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
