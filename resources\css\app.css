@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=Dancing+Script:wght@400;500;600;700&display=swap');

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Poppins', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
    --font-display: 'Dancing Script', cursive;

    /* Garba Color Palette */
    --color-garba-red: #dc2626;
    --color-garba-orange: #ea580c;
    --color-garba-gold: #d97706;
    --color-garba-yellow: #eab308;
    --color-garba-blue: #1d4ed8;
    --color-garba-green: #16a34a;
    --color-garba-purple: #7c3aed;
    --color-garba-pink: #ec4899;

    /* Custom Colors */
    --color-primary: var(--color-garba-red);
    --color-secondary: var(--color-garba-gold);
    --color-accent: var(--color-garba-orange);

    /* Gradients */
    --gradient-garba: linear-gradient(135deg, var(--color-garba-red), var(--color-garba-orange), var(--color-garba-gold));
    --gradient-festive: linear-gradient(45deg, var(--color-garba-purple), var(--color-garba-pink), var(--color-garba-orange));
}

/* Custom Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(220, 38, 38, 0.3); }
    50% { box-shadow: 0 0 30px rgba(220, 38, 38, 0.6); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Garba Pattern Background */
.garba-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(234, 88, 12, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, rgba(217, 119, 6, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 25% 75%, rgba(29, 78, 216, 0.1) 0%, transparent 50%);
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--color-accent);
}

/* Navigation Styles */
.nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-red-600 hover:bg-red-50 transition-all duration-200;
}

.nav-link.active {
    @apply text-red-600 bg-red-50 font-semibold;
}

.mobile-nav-link {
    @apply block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-red-600 hover:bg-red-50 transition-all duration-200;
}

.mobile-nav-link.active {
    @apply text-red-600 bg-red-50 font-semibold;
}

/* Button Styles */
.btn-primary {
    @apply inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-500 to-orange-500 text-white font-semibold rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-secondary {
    @apply inline-flex items-center px-4 py-2 bg-white text-red-600 font-semibold rounded-lg border-2 border-red-500 hover:bg-red-50 transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-outline {
    @apply inline-flex items-center px-4 py-2 bg-transparent text-gray-700 font-semibold rounded-lg border-2 border-gray-300 hover:border-red-500 hover:text-red-600 transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-large {
    @apply px-8 py-4 text-lg;
}

.btn-small {
    @apply px-3 py-1 text-sm;
}

/* Card Styles */
.card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden;
}

.card-hover {
    @apply transform hover:scale-105 hover:-translate-y-2;
}

/* Form Styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
    @apply text-red-600 text-sm mt-1;
}

/* Hero Section Styles */
.hero-gradient {
    background: linear-gradient(135deg,
        rgba(220, 38, 38, 0.9) 0%,
        rgba(234, 88, 12, 0.9) 50%,
        rgba(217, 119, 6, 0.9) 100%);
}

/* Text Gradients */
.text-gradient {
    background: linear-gradient(135deg, var(--color-garba-red), var(--color-garba-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-gold {
    background: linear-gradient(135deg, var(--color-garba-gold), var(--color-garba-yellow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Notification styles */
.notification {
    @apply fixed top-4 right-4 z-50 max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform translate-x-full transition-transform duration-300;
}

.notification.show {
    @apply translate-x-0;
}

.notification.success {
    @apply border-green-500;
}

.notification.error {
    @apply border-red-500;
}

.notification.warning {
    @apply border-yellow-500;
}

.notification.info {
    @apply border-blue-500;
}

/* Mobile-first responsive improvements */
@media (max-width: 640px) {
    .notification {
        @apply top-2 right-2 left-2 max-w-none;
    }

    .hero-title {
        @apply text-4xl;
    }

    .hero-subtitle {
        @apply text-lg;
    }

    .card {
        @apply mx-2;
    }

    .btn-large {
        @apply text-base px-6 py-3;
    }

    .nav-link {
        @apply text-sm;
    }
}

/* Enhanced animations for mobile */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Logo Animations */
@keyframes logoSpin {
    0% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(90deg) scale(1.05); }
    50% { transform: rotate(180deg) scale(1); }
    75% { transform: rotate(270deg) scale(1.05); }
    100% { transform: rotate(360deg) scale(1); }
}

@keyframes logoBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(0deg); }
    40% { transform: translateY(-10px) rotate(5deg); }
    60% { transform: translateY(-5px) rotate(-5deg); }
}

@keyframes logoGlow {
    0%, 100% {
        filter: drop-shadow(0 0 10px rgba(220, 38, 38, 0.5));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(234, 88, 12, 0.8));
        transform: scale(1.05);
    }
}

/* Particle Animation */
@keyframes particle {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* Festive Sparkle */
@keyframes sparkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1) rotate(180deg);
    }
}

/* Dancing Elements */
@keyframes dance {
    0%, 100% { transform: translateX(0) rotate(0deg); }
    25% { transform: translateX(-5px) rotate(-2deg); }
    50% { transform: translateX(5px) rotate(2deg); }
    75% { transform: translateX(-3px) rotate(-1deg); }
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out;
}

/* Logo Animation Classes */
.animate-logo-spin {
    animation: logoSpin 8s linear infinite;
}

.animate-logo-bounce {
    animation: logoBounce 2s ease-in-out infinite;
}

.animate-logo-glow {
    animation: logoGlow 3s ease-in-out infinite;
}

.animate-particle {
    animation: particle 3s linear infinite;
}

.animate-sparkle {
    animation: sparkle 1.5s ease-in-out infinite;
}

.animate-dance {
    animation: dance 4s ease-in-out infinite;
}

/* Festive Background Patterns */
.festive-bg {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(234, 88, 12, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(217, 119, 6, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 60% 40%, rgba(29, 78, 216, 0.1) 0%, transparent 50%);
    background-size: 300px 300px, 400px 400px, 350px 350px, 250px 250px;
    animation: dance 20s ease-in-out infinite;
}

/* Floating Particles */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: linear-gradient(45deg, #dc2626, #ea580c);
    border-radius: 50%;
    pointer-events: none;
}

.particle:nth-child(odd) {
    background: linear-gradient(45deg, #d97706, #eab308);
    animation-delay: -1s;
}

.particle:nth-child(3n) {
    background: linear-gradient(45deg, #1d4ed8, #7c3aed);
    animation-delay: -2s;
}

/* Music Control Styles */
.music-control {
    @apply fixed bottom-6 left-6 z-50 w-14 h-14 bg-gradient-to-br from-red-500 to-orange-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center cursor-pointer;
}

.music-control:hover {
    transform: scale(1.1);
}

.music-control.playing {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced Card Animations */
.card-enhanced {
    @apply bg-white/90 backdrop-blur-sm rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden relative;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.card-enhanced:hover::before {
    left: 100%;
}

/* Glowing Text Effect */
.text-glow {
    text-shadow: 0 0 10px rgba(220, 38, 38, 0.5), 0 0 20px rgba(234, 88, 12, 0.3);
}

/* Interactive Hover Effects */
.interactive-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-hover:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .btn-primary:hover,
    .btn-secondary:hover,
    .nav-link:hover {
        transform: none;
    }

    .card:hover {
        transform: none;
    }

    .btn-primary:active,
    .btn-secondary:active {
        transform: scale(0.98);
    }
}

/* Loading states */
.loading-spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Improved form styles for mobile */
@media (max-width: 768px) {
    .form-input {
        @apply text-base; /* Prevents zoom on iOS */
    }

    .form-grid {
        @apply grid-cols-1;
    }
}

/* Accessibility improvements */
.sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Focus styles */
.focus-visible:focus {
    @apply outline-none ring-2 ring-red-500 ring-offset-2;
}

/* Animation on scroll */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }
}
