@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 text-white overflow-hidden">
    <div class="absolute inset-0 garba-pattern opacity-20"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h1 class="text-5xl md:text-6xl font-bold font-display mb-6">Community Polls</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Share your voice and help us make this year's Garba celebration even better
            </p>
        </div>
    </div>
</section>

<!-- Active Polls -->
<section class="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl font-bold text-gradient mb-4">Active Polls</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Your opinion matters! Vote on these important topics to help shape our event.
            </p>
        </div>

        <div class="space-y-8">
            <!-- Poll 1: Favorite Music Style -->
            <div class="poll-card card p-8 animate-on-scroll" data-poll-id="1">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">What's your favorite Garba music style?</h3>
                        <p class="text-gray-600">Help us choose the perfect mix of traditional and modern music for the event.</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Ends in</div>
                        <div class="text-lg font-semibold text-red-600">5 days</div>
                    </div>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="poll-option" data-option-id="1">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_1" value="traditional" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Traditional Folk Music</div>
                                <div class="text-sm text-gray-600">Classic Gujarati folk songs and traditional instruments</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">45%</div>
                                <div class="text-sm text-gray-500">127 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-500" style="width: 45%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="2">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_1" value="modern" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Modern Garba Fusion</div>
                                <div class="text-sm text-gray-600">Contemporary beats mixed with traditional melodies</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">35%</div>
                                <div class="text-sm text-gray-500">98 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-orange-500 to-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 35%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="3">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_1" value="mixed" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Mixed Playlist</div>
                                <div class="text-sm text-gray-600">Equal mix of traditional and modern music</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">20%</div>
                                <div class="text-sm text-gray-500">56 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-yellow-500 to-green-500 h-2 rounded-full transition-all duration-500" style="width: 20%"></div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        281 total votes
                    </div>
                    <button class="btn-primary vote-btn" data-poll-id="1">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Vote Now
                    </button>
                </div>
            </div>

            <!-- Poll 2: Event Duration -->
            <div class="poll-card card p-8 animate-on-scroll" data-poll-id="2">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">How long should the main event be?</h3>
                        <p class="text-gray-600">Help us plan the perfect duration for maximum enjoyment without fatigue.</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Ends in</div>
                        <div class="text-lg font-semibold text-red-600">3 days</div>
                    </div>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="poll-option" data-option-id="4">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_2" value="4hours" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">4 Hours (6 PM - 10 PM)</div>
                                <div class="text-sm text-gray-600">Perfect for families with children</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">30%</div>
                                <div class="text-sm text-gray-500">67 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500" style="width: 30%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="5">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_2" value="6hours" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">6 Hours (6 PM - 12 AM)</div>
                                <div class="text-sm text-gray-600">Extended celebration with multiple sessions</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">55%</div>
                                <div class="text-sm text-gray-500">123 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 55%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="6">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="radio" name="poll_2" value="8hours" class="w-5 h-5 text-red-600 border-gray-300 focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">8 Hours (6 PM - 2 AM)</div>
                                <div class="text-sm text-gray-600">All-night celebration for the ultimate experience</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">15%</div>
                                <div class="text-sm text-gray-500">34 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500" style="width: 15%"></div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        224 total votes
                    </div>
                    <button class="btn-primary vote-btn" data-poll-id="2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Vote Now
                    </button>
                </div>
            </div>

            <!-- Poll 3: Food Preferences -->
            <div class="poll-card card p-8 animate-on-scroll" data-poll-id="3">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-2">Which food options would you prefer?</h3>
                        <p class="text-gray-600">Multiple selections allowed. Help us plan the perfect menu for everyone.</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Ends in</div>
                        <div class="text-lg font-semibold text-red-600">7 days</div>
                    </div>
                </div>

                <div class="space-y-4 mb-6">
                    <div class="poll-option" data-option-id="7">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="checkbox" name="poll_3[]" value="gujarati_thali" class="w-5 h-5 text-red-600 border-gray-300 rounded focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Traditional Gujarati Thali</div>
                                <div class="text-sm text-gray-600">Complete traditional meal with dal, sabzi, roti, and sweets</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">78%</div>
                                <div class="text-sm text-gray-500">195 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-500" style="width: 78%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="8">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="checkbox" name="poll_3[]" value="street_food" class="w-5 h-5 text-red-600 border-gray-300 rounded focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Street Food Stalls</div>
                                <div class="text-sm text-gray-600">Dhokla, khandvi, fafda, and other popular snacks</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">65%</div>
                                <div class="text-sm text-gray-500">162 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-orange-500 to-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 65%"></div>
                        </div>
                    </div>

                    <div class="poll-option" data-option-id="9">
                        <label class="flex items-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-red-300 hover:bg-red-50 transition-all duration-200">
                            <input type="checkbox" name="poll_3[]" value="sweets" class="w-5 h-5 text-red-600 border-gray-300 rounded focus:ring-red-500">
                            <div class="ml-4 flex-grow">
                                <div class="font-semibold text-gray-800">Traditional Sweets Counter</div>
                                <div class="text-sm text-gray-600">Jalebi, gulab jamun, ras malai, and seasonal sweets</div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">52%</div>
                                <div class="text-sm text-gray-500">130 votes</div>
                            </div>
                        </label>
                        <div class="mt-2 bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-yellow-500 to-green-500 h-2 rounded-full transition-all duration-500" style="width: 52%"></div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        250 total votes
                    </div>
                    <button class="btn-primary vote-btn" data-poll-id="3">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Vote Now
                    </button>
                </div>
            </div>
        </div>

        <!-- Create New Poll -->
        <div class="mt-16 animate-on-scroll">
            <div class="card p-8 text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4">Have a Suggestion?</h3>
                <p class="text-gray-600 mb-6 max-w-2xl mx-auto">
                    Want to create a poll about something specific? Share your ideas with the community and let everyone vote on what matters most.
                </p>
                <button class="btn-secondary btn-large" id="suggest-poll-btn">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    Suggest a Poll
                </button>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Vote button functionality
        document.querySelectorAll('.vote-btn').forEach(button => {
            button.addEventListener('click', function() {
                const pollId = this.dataset.pollId;
                const pollCard = document.querySelector(`[data-poll-id="${pollId}"]`);
                
                // Get selected options
                let selectedOptions = [];
                const radioInputs = pollCard.querySelectorAll('input[type="radio"]:checked');
                const checkboxInputs = pollCard.querySelectorAll('input[type="checkbox"]:checked');
                
                radioInputs.forEach(input => selectedOptions.push(input.value));
                checkboxInputs.forEach(input => selectedOptions.push(input.value));
                
                if (selectedOptions.length === 0) {
                    showNotification('Please select at least one option before voting.', 'warning');
                    return;
                }
                
                // Show loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Voting...';
                this.disabled = true;
                
                // Simulate vote submission
                setTimeout(() => {
                    // Update vote counts (in a real app, this would come from the server)
                    updateVoteCounts(pollId, selectedOptions);
                    
                    // Show success message
                    showNotification('Thank you for voting! Your voice has been heard.', 'success');
                    
                    // Disable voting for this poll
                    pollCard.querySelectorAll('input').forEach(input => input.disabled = true);
                    pollCard.querySelectorAll('label').forEach(label => label.classList.add('opacity-75', 'cursor-not-allowed'));
                    
                    // Update button
                    this.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Voted';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-secondary');
                    this.disabled = true;
                }, 1500);
            });
        });
        
        // Suggest poll functionality
        document.getElementById('suggest-poll-btn').addEventListener('click', function() {
            // In a real application, this would open a modal or redirect to a form
            showNotification('Poll suggestion feature coming soon! Contact us directly for now.', 'info');
        });
        
        // Interactive poll option highlighting
        document.querySelectorAll('.poll-option input').forEach(input => {
            input.addEventListener('change', function() {
                const label = this.closest('label');
                const pollOption = this.closest('.poll-option');
                
                if (this.type === 'radio') {
                    // Remove selection from other options in the same poll
                    const pollCard = this.closest('.poll-card');
                    pollCard.querySelectorAll('.poll-option label').forEach(l => {
                        l.classList.remove('border-red-500', 'bg-red-50');
                    });
                }
                
                if (this.checked) {
                    label.classList.add('border-red-500', 'bg-red-50');
                } else {
                    label.classList.remove('border-red-500', 'bg-red-50');
                }
            });
        });
    });
    
    function updateVoteCounts(pollId, selectedOptions) {
        // This is a simulation - in a real app, vote counts would be updated from the server
        const pollCard = document.querySelector(`[data-poll-id="${pollId}"]`);
        
        selectedOptions.forEach(optionValue => {
            const input = pollCard.querySelector(`input[value="${optionValue}"]`);
            if (input) {
                const pollOption = input.closest('.poll-option');
                const voteCountElement = pollOption.querySelector('.text-lg.font-bold');
                const voteTextElement = pollOption.querySelector('.text-sm.text-gray-500');
                const progressBar = pollOption.querySelector('.bg-gradient-to-r');
                
                // Simulate vote count increase
                const currentVotes = parseInt(voteTextElement.textContent.match(/\d+/)[0]);
                const newVotes = currentVotes + 1;
                voteTextElement.textContent = `${newVotes} votes`;
                
                // Update percentage (simplified calculation)
                const newPercentage = Math.min(100, parseInt(voteCountElement.textContent) + 1);
                voteCountElement.textContent = `${newPercentage}%`;
                progressBar.style.width = `${newPercentage}%`;
            }
        });
    }
</script>

<style>
.poll-card {
    @apply transition-all duration-300;
}

.poll-card:hover {
    @apply transform -translate-y-1 shadow-xl;
}

.poll-option label:hover {
    @apply shadow-md;
}

.poll-option input:disabled + div {
    @apply opacity-75;
}
</style>
@endpush
@endsection
