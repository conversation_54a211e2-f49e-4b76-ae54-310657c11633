@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 text-white overflow-hidden">
    <div class="absolute inset-0 garba-pattern opacity-20"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h1 class="text-5xl md:text-6xl font-bold font-display mb-6">Gallery</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Relive the magical moments from our previous Garba celebrations
            </p>
        </div>
    </div>
</section>

<!-- Gallery Filter -->
<section class="py-8 bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="gallery-filter-btn active" data-filter="all">All Photos</button>
            <button class="gallery-filter-btn" data-filter="dance">Dance</button>
            <button class="gallery-filter-btn" data-filter="music">Music</button>
            <button class="gallery-filter-btn" data-filter="food">Food</button>
            <button class="gallery-filter-btn" data-filter="costumes">Costumes</button>
            <button class="gallery-filter-btn" data-filter="crowd">Crowd</button>
        </div>
    </div>
</section>

<!-- Photo Gallery -->
<section class="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="gallery-grid">
            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="dance">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-red-200 to-orange-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-red-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            <p class="text-red-600 font-semibold">Traditional Dance</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="music">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-orange-200 to-yellow-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-orange-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                            </svg>
                            <p class="text-orange-600 font-semibold">Live Music</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="costumes">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-yellow-200 to-green-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-yellow-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <p class="text-yellow-600 font-semibold">Traditional Costumes</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="food">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-green-200 to-blue-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-green-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <p class="text-green-600 font-semibold">Delicious Food</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="crowd">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-blue-200 to-purple-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-blue-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <p class="text-blue-600 font-semibold">Crowd Moments</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="dance">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-purple-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path>
                            </svg>
                            <p class="text-purple-600 font-semibold">Dandiya Dance</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="costumes">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-pink-200 to-red-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-pink-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                            <p class="text-pink-600 font-semibold">Costume Contest</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gallery Item -->
            <div class="gallery-item animate-on-scroll" data-category="crowd">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <div class="aspect-square bg-gradient-to-br from-indigo-200 to-purple-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-indigo-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <p class="text-indigo-600 font-semibold">Community Joy</p>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                        <button class="opacity-0 group-hover:opacity-100 bg-white text-gray-800 px-4 py-2 rounded-lg font-semibold transform scale-90 group-hover:scale-100 transition-all duration-300">
                            View Full Size
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Load More Button -->
        <div class="text-center mt-12 animate-on-scroll">
            <button id="load-more-btn" class="btn-primary btn-large">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                Load More Photos
            </button>
        </div>
    </div>
</section>

<!-- Video Gallery Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl font-bold text-gradient mb-4">Video Highlights</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the energy and excitement through our video collection from previous events
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Video Item -->
            <div class="animate-on-scroll">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <div class="aspect-video bg-gradient-to-br from-red-200 to-orange-200 flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-80 rounded-full flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-8 h-8 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800">Opening Ceremony 2023</h3>
                            <p class="text-gray-600 text-sm">5:32 minutes</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Item -->
            <div class="animate-on-scroll">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <div class="aspect-video bg-gradient-to-br from-orange-200 to-yellow-200 flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-80 rounded-full flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-8 h-8 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800">Traditional Garba Dance</h3>
                            <p class="text-gray-600 text-sm">8:15 minutes</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Video Item -->
            <div class="animate-on-scroll">
                <div class="relative group overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300">
                    <div class="aspect-video bg-gradient-to-br from-yellow-200 to-green-200 flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-80 rounded-full flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-8 h-8 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800">Grand Finale Highlights</h3>
                            <p class="text-gray-600 text-sm">12:45 minutes</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    // Gallery filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.gallery-filter-btn');
        const galleryItems = document.querySelectorAll('.gallery-item');
        const loadMoreBtn = document.getElementById('load-more-btn');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Filter gallery items
                galleryItems.forEach(item => {
                    const category = item.getAttribute('data-category');
                    if (filter === 'all' || category === filter) {
                        item.style.display = 'block';
                        item.classList.add('animate-on-scroll');
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Load more functionality (placeholder)
        loadMoreBtn.addEventListener('click', function() {
            this.textContent = 'Loading...';
            this.disabled = true;
            
            // Simulate loading
            setTimeout(() => {
                this.textContent = 'Load More Photos';
                this.disabled = false;
                // In a real application, you would load more photos here
                showNotification('More photos loaded!', 'success');
            }, 2000);
        });
    });
</script>

<style>
.gallery-filter-btn {
    @apply px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-purple-50 hover:text-purple-600 hover:border-purple-300 transition-all duration-200 font-medium;
}

.gallery-filter-btn.active {
    @apply bg-purple-500 text-white border-purple-500;
}

.gallery-item {
    @apply transition-all duration-300;
}
</style>
@endpush
@endsection
