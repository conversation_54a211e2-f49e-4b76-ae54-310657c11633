@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white overflow-hidden">
    <div class="absolute inset-0 garba-pattern opacity-20"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h1 class="text-5xl md:text-6xl font-bold font-display mb-6">Book Your Pass</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Secure your spot at the most vibrant Garba celebration of the year
            </p>
        </div>
    </div>
</section>

<!-- Pass Options -->
<section class="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl font-bold text-gradient mb-4">Choose Your Pass</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Select the perfect pass for your Garba experience. All passes include access to traditional dance, live music, and authentic cuisine.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <!-- Basic Pass -->
            <div class="pass-card animate-on-scroll" data-pass="basic" data-price="299">
                <div class="card p-8 text-center relative overflow-hidden">
                    <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-400 to-blue-500"></div>
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">Basic Pass</h3>
                    <div class="text-4xl font-bold text-gradient mb-6">₹299</div>
                    <ul class="space-y-3 text-gray-600 mb-8">
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Entry to all dance sessions
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Access to food court
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Live music performances
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Basic parking
                        </li>
                    </ul>
                    <button class="btn-secondary w-full select-pass-btn">Select Basic Pass</button>
                </div>
            </div>

            <!-- Premium Pass -->
            <div class="pass-card animate-on-scroll" data-pass="premium" data-price="499">
                <div class="card p-8 text-center relative overflow-hidden">
                    <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 to-red-500"></div>
                    <div class="absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">Popular</div>
                    <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">Premium Pass</h3>
                    <div class="text-4xl font-bold text-gradient mb-6">₹499</div>
                    <ul class="space-y-3 text-gray-600 mb-8">
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            All Basic Pass features
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Complimentary dinner
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Priority seating area
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Free Dandiya sticks
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Event merchandise
                        </li>
                    </ul>
                    <button class="btn-primary w-full select-pass-btn">Select Premium Pass</button>
                </div>
            </div>

            <!-- VIP Pass -->
            <div class="pass-card animate-on-scroll" data-pass="vip" data-price="799">
                <div class="card p-8 text-center relative overflow-hidden">
                    <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400 to-orange-500"></div>
                    <div class="absolute top-4 right-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">VIP</div>
                    <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">VIP Pass</h3>
                    <div class="text-4xl font-bold text-gradient mb-6">₹799</div>
                    <ul class="space-y-3 text-gray-600 mb-8">
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            All Premium Pass features
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            VIP lounge access
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Meet & greet with artists
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Premium parking
                        </li>
                        <li class="flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Professional photography
                        </li>
                    </ul>
                    <button class="btn-primary w-full select-pass-btn">Select VIP Pass</button>
                </div>
            </div>
        </div>

        <!-- Booking Form -->
        <div class="max-w-4xl mx-auto animate-on-scroll" id="booking-form-section" style="display: none;">
            <div class="card p-8">
                <div class="flex items-center justify-between mb-8">
                    <h3 class="text-3xl font-bold text-gradient">Complete Your Booking</h3>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">Selected Pass:</div>
                        <div class="text-xl font-bold text-gray-800" id="selected-pass-name">Premium Pass</div>
                        <div class="text-2xl font-bold text-gradient" id="selected-pass-price">₹499</div>
                    </div>
                </div>

                <form id="booking-form" class="space-y-8">
                    @csrf
                    <input type="hidden" name="pass_type" id="pass_type" value="">
                    <input type="hidden" name="pass_price" id="pass_price" value="">

                    <!-- Personal Information -->
                    <div>
                        <h4 class="text-xl font-semibold text-gray-800 mb-4">Personal Information</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label">Full Name *</label>
                                <input type="text" name="full_name" class="form-input" placeholder="Enter your full name" required>
                                <div class="form-error" id="full_name-error"></div>
                            </div>
                            <div>
                                <label class="form-label">Email Address *</label>
                                <input type="email" name="email" class="form-input" placeholder="<EMAIL>" required>
                                <div class="form-error" id="email-error"></div>
                            </div>
                            <div>
                                <label class="form-label">Phone Number *</label>
                                <input type="tel" name="phone" class="form-input" placeholder="+91 12345 67890" required>
                                <div class="form-error" id="phone-error"></div>
                            </div>
                            <div>
                                <label class="form-label">Age *</label>
                                <input type="number" name="age" class="form-input" placeholder="25" min="5" max="100" required>
                                <div class="form-error" id="age-error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Details -->
                    <div>
                        <h4 class="text-xl font-semibold text-gray-800 mb-4">Additional Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label">Number of Passes *</label>
                                <select name="quantity" class="form-input" id="quantity-select" required>
                                    <option value="1">1 Pass</option>
                                    <option value="2">2 Passes</option>
                                    <option value="3">3 Passes</option>
                                    <option value="4">4 Passes</option>
                                    <option value="5">5 Passes</option>
                                </select>
                                <div class="form-error" id="quantity-error"></div>
                            </div>
                            <div>
                                <label class="form-label">T-Shirt Size</label>
                                <select name="tshirt_size" class="form-input">
                                    <option value="">Select Size (Optional)</option>
                                    <option value="XS">XS</option>
                                    <option value="S">S</option>
                                    <option value="M">M</option>
                                    <option value="L">L</option>
                                    <option value="XL">XL</option>
                                    <option value="XXL">XXL</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-6">
                            <label class="form-label">Special Requirements</label>
                            <textarea name="special_requirements" rows="3" class="form-input" placeholder="Any dietary restrictions, accessibility needs, or other special requirements..."></textarea>
                        </div>
                    </div>

                    <!-- Total Amount -->
                    <div class="bg-gradient-to-r from-red-50 to-orange-50 p-6 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="text-lg font-semibold text-gray-800">Total Amount</div>
                                <div class="text-sm text-gray-600" id="price-breakdown">1 × Premium Pass</div>
                            </div>
                            <div class="text-3xl font-bold text-gradient" id="total-amount">₹499</div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-start space-x-3">
                        <input type="checkbox" id="terms" name="terms" class="w-5 h-5 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 mt-1" required>
                        <label for="terms" class="text-sm text-gray-600">
                            I agree to the <a href="#" class="text-red-600 hover:text-red-700 underline">Terms and Conditions</a> and <a href="#" class="text-red-600 hover:text-red-700 underline">Privacy Policy</a>. I understand that all bookings are final and non-refundable.
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="button" class="btn-secondary flex-1" id="back-to-passes">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            Back to Passes
                        </button>
                        <button type="submit" class="btn-primary flex-1 btn-large">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            Proceed to Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const passCards = document.querySelectorAll('.pass-card');
        const bookingFormSection = document.getElementById('booking-form-section');
        const bookingForm = document.getElementById('booking-form');
        const backToPassesBtn = document.getElementById('back-to-passes');
        const quantitySelect = document.getElementById('quantity-select');

        // Pass selection
        document.querySelectorAll('.select-pass-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const passCard = this.closest('.pass-card');
                const passType = passCard.dataset.pass;
                const passPrice = parseInt(passCard.dataset.price);
                
                // Update form
                document.getElementById('pass_type').value = passType;
                document.getElementById('pass_price').value = passPrice;
                document.getElementById('selected-pass-name').textContent = passType.charAt(0).toUpperCase() + passType.slice(1) + ' Pass';
                document.getElementById('selected-pass-price').textContent = '₹' + passPrice;
                
                // Show booking form
                bookingFormSection.style.display = 'block';
                bookingFormSection.scrollIntoView({ behavior: 'smooth' });
                
                // Update total
                updateTotal();
            });
        });

        // Back to passes
        backToPassesBtn.addEventListener('click', function() {
            bookingFormSection.style.display = 'none';
            document.querySelector('.pass-card').scrollIntoView({ behavior: 'smooth' });
        });

        // Quantity change
        quantitySelect.addEventListener('change', updateTotal);

        function updateTotal() {
            const basePrice = parseInt(document.getElementById('pass_price').value) || 0;
            const quantity = parseInt(quantitySelect.value) || 1;
            const passName = document.getElementById('selected-pass-name').textContent;
            const total = basePrice * quantity;
            
            document.getElementById('price-breakdown').textContent = `${quantity} × ${passName}`;
            document.getElementById('total-amount').textContent = '₹' + total;
        }

        // Form submission
        bookingForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            document.querySelectorAll('.form-error').forEach(error => {
                error.textContent = '';
            });

            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<svg class="w-5 h-5 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Processing...';
            submitButton.disabled = true;

            // Prepare form data
            const formData = new FormData(this);

            // Submit form via AJAX
            fetch('{{ route("booking.store") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    // Redirect to payment page
                    setTimeout(() => {
                        window.location.href = data.redirect_url;
                    }, 1500);
                } else {
                    // Show validation errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const errorElement = document.getElementById(field + '-error');
                            if (errorElement) {
                                errorElement.textContent = data.errors[field][0];
                            }
                        });
                    }
                    showNotification(data.message || 'Please check the form for errors.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        });
    });
</script>

<style>
.pass-card {
    @apply transition-all duration-300;
}

.pass-card:hover {
    @apply transform -translate-y-2;
}

.pass-card:hover .card {
    @apply shadow-2xl;
}
</style>
@endpush
@endsection
