<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\PollController;

// Home route
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', function () {
    return view('pages.about');
})->name('about');
Route::get('/schedule', function () {
    return view('pages.schedule');
})->name('schedule');
Route::get('/gallery', function () {
    return view('pages.gallery');
})->name('gallery');
Route::get('/contact', function () {
    return view('pages.contact');
})->name('contact');

// Authentication Routes
Route::group(['prefix' => 'auth'], function () {
    // Login Routes
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // Registration Routes
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);

    // Password Reset Routes
    Route::get('/password/reset', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/password/reset/{token}', [ForgotPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [ForgotPasswordController::class, 'reset'])->name('password.update');
});



// Protected Routes (require authentication)
Route::middleware('auth')->group(function () {
    // Booking routes
    Route::get('/book-pass', [BookingController::class, 'showBookingForm'])->name('book-pass');
    Route::post('/book-pass', [BookingController::class, 'store'])->name('booking.store');
    Route::get('/booking/{booking}/payment', [BookingController::class, 'showPayment'])->name('booking.payment');
    Route::post('/booking/{booking}/payment', [BookingController::class, 'processPayment'])->name('booking.process-payment');
    Route::get('/booking/{booking}/confirmation', [BookingController::class, 'showConfirmation'])->name('booking.confirmation');
    Route::get('/my-bookings', [BookingController::class, 'myBookings'])->name('my-bookings');

    // Poll routes
    Route::get('/poll', [PollController::class, 'index'])->name('poll');
    Route::post('/poll/{poll}/vote', [PollController::class, 'vote'])->name('poll.vote');
    Route::get('/poll/{poll}/results', [PollController::class, 'results'])->name('poll.results');

    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});
