<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\Event;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display the booking form
     */
    public function showBookingForm()
    {
        return view('pages.book-pass');
    }

    /**
     * Store a new booking
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pass_type' => 'required|in:basic,premium,vip',
            'pass_price' => 'required|numeric|min:0',
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'age' => 'required|integer|min:5|max:100',
            'quantity' => 'required|integer|min:1|max:10',
            'tshirt_size' => 'nullable|in:XS,S,M,L,XL,XXL',
            'special_requirements' => 'nullable|string|max:1000',
            'terms' => 'required|accepted',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get the current event (you might want to make this dynamic)
            $event = Event::where('is_active', true)->first();

            if (!$event) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active event found.'
                ], 404);
            }

            // Calculate total amount
            $totalAmount = $request->pass_price * $request->quantity;

            // Create booking
            $booking = Booking::create([
                'user_id' => Auth::id(),
                'event_id' => $event->id,
                'pass_type' => $request->pass_type,
                'quantity' => $request->quantity,
                'total_amount' => $totalAmount,
                'attendee_name' => $request->full_name,
                'attendee_email' => $request->email,
                'attendee_phone' => $request->phone,
                'attendee_age' => $request->age,
                'tshirt_size' => $request->tshirt_size,
                'special_requirements' => $request->special_requirements,
                'booking_status' => 'pending',
                'payment_status' => 'pending',
                'booking_reference' => 'RG' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully!',
                'booking_id' => $booking->id,
                'booking_reference' => $booking->booking_reference,
                'total_amount' => $totalAmount,
                'redirect_url' => route('booking.payment', $booking->id)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your booking. Please try again.'
            ], 500);
        }
    }

    /**
     * Show payment page
     */
    public function showPayment($bookingId)
    {
        $booking = Booking::where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->with('event')
            ->first();

        if (!$booking) {
            abort(404, 'Booking not found.');
        }

        return view('pages.payment', compact('booking'));
    }

    /**
     * Process payment (placeholder for payment gateway integration)
     */
    public function processPayment(Request $request, $bookingId)
    {
        $booking = Booking::where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$booking) {
            return response()->json([
                'success' => false,
                'message' => 'Booking not found.'
            ], 404);
        }

        // In a real application, you would integrate with a payment gateway here
        // For demo purposes, we'll simulate a successful payment

        $booking->update([
            'payment_status' => 'completed',
            'booking_status' => 'confirmed',
            'payment_method' => $request->payment_method ?? 'demo',
            'payment_reference' => 'PAY' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment successful! Your booking is confirmed.',
            'booking_reference' => $booking->booking_reference,
            'redirect_url' => route('booking.confirmation', $booking->id)
        ]);
    }

    /**
     * Show booking confirmation
     */
    public function showConfirmation($bookingId)
    {
        $booking = Booking::where('id', $bookingId)
            ->where('user_id', Auth::id())
            ->with('event')
            ->first();

        if (!$booking) {
            abort(404, 'Booking not found.');
        }

        return view('pages.booking-confirmation', compact('booking'));
    }

    /**
     * Show user's bookings
     */
    public function myBookings()
    {
        $bookings = Booking::where('user_id', Auth::id())
            ->with('event')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.my-bookings', compact('bookings'));
    }
}
