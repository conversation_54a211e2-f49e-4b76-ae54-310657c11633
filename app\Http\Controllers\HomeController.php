<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Event;

class HomeController extends Controller
{
    /**
     * Show the home page
     */
    public function index()
    {
        // Get featured events
        $featuredEvents = Event::active()->featured()->upcoming()->take(3)->get();

        // Get the main event (first active event)
        $mainEvent = Event::active()->upcoming()->first();

        return view('home', compact('featuredEvents', 'mainEvent'));
    }
}
