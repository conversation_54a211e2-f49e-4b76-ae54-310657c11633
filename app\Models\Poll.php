<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Poll extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'options',
        'is_active',
        'allow_multiple_responses',
        'starts_at',
        'ends_at',
        'max_responses',
        'total_responses',
    ];

    protected function casts(): array
    {
        return [
            'options' => 'array',
            'is_active' => 'boolean',
            'allow_multiple_responses' => 'boolean',
            'starts_at' => 'datetime',
            'ends_at' => 'datetime',
        ];
    }

    /**
     * Get the poll responses
     */
    public function responses()
    {
        return $this->hasMany(PollResponse::class);
    }

    /**
     * Scope for active polls
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for currently running polls
     */
    public function scopeRunning($query)
    {
        $now = now();
        return $query->where('is_active', true)
                    ->where(function ($q) use ($now) {
                        $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
                    })
                    ->where(function ($q) use ($now) {
                        $q->whereNull('ends_at')->orWhere('ends_at', '>=', $now);
                    });
    }

    /**
     * Check if poll is currently running
     */
    public function isRunning(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();

        if ($this->starts_at && $this->starts_at > $now) {
            return false;
        }

        if ($this->ends_at && $this->ends_at < $now) {
            return false;
        }

        return true;
    }

    /**
     * Check if user has already responded
     */
    public function hasUserResponded(User $user): bool
    {
        return $this->responses()->where('user_id', $user->id)->exists();
    }

    /**
     * Get poll results
     */
    public function getResults(): array
    {
        $responses = $this->responses()->get();
        $results = [];

        foreach ($responses as $response) {
            $responseData = $response->response_data;

            if ($this->type === 'single_choice' || $this->type === 'multiple_choice') {
                foreach ((array) $responseData as $option) {
                    $results[$option] = ($results[$option] ?? 0) + 1;
                }
            } elseif ($this->type === 'rating') {
                $results['ratings'][] = $responseData;
            } elseif ($this->type === 'text') {
                $results['responses'][] = $responseData;
            }
        }

        return $results;
    }
}
