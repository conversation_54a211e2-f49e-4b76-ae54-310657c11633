<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class EventSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'title',
        'description',
        'schedule_date',
        'start_time',
        'end_time',
        'stage_location',
        'activity_type',
        'performer_artist',
        'is_featured',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'schedule_date' => 'date',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Get the event that owns the schedule
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scope for featured schedules
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for specific activity type
     */
    public function scopeByActivityType($query, $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope for specific date
     */
    public function scopeByDate($query, $date)
    {
        return $query->where('schedule_date', $date);
    }

    /**
     * Get formatted time range
     */
    public function getTimeRangeAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }
}
