@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-orange-600 via-red-600 to-pink-600 text-white overflow-hidden">
    <div class="absolute inset-0 garba-pattern opacity-20"></div>
    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="animate-on-scroll">
            <h1 class="text-5xl md:text-6xl font-bold font-display mb-6">Event Schedule</h1>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Plan your perfect Garba experience with our detailed event timeline
            </p>
        </div>
    </div>
</section>

<!-- Schedule Filter -->
<section class="py-8 bg-white border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="schedule-filter-btn active" data-filter="all">All Events</button>
            <button class="schedule-filter-btn" data-filter="dance">Dance</button>
            <button class="schedule-filter-btn" data-filter="music">Music</button>
            <button class="schedule-filter-btn" data-filter="food">Food</button>
            <button class="schedule-filter-btn" data-filter="cultural">Cultural</button>
            <button class="schedule-filter-btn" data-filter="competition">Competition</button>
        </div>
    </div>
</section>

<!-- Main Schedule -->
<section class="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Day 1 Schedule -->
        <div class="mb-16 animate-on-scroll">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-gradient mb-4">Day 1 - October 15, 2024</h2>
                <p class="text-xl text-gray-600">Opening Ceremony & Traditional Garba</p>
            </div>

            <div class="space-y-6">
                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="cultural">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">6:00</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Opening Ceremony</h3>
                            <span class="px-3 py-1 bg-red-100 text-red-600 rounded-full text-sm font-semibold">Cultural</span>
                            <span class="px-3 py-1 bg-yellow-100 text-yellow-600 rounded-full text-sm font-semibold">Featured</span>
                        </div>
                        <p class="text-gray-600 mb-2">Traditional lamp lighting ceremony and welcome address by community leaders</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Main Stage
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">6:00 - 6:30 PM</div>
                        <div class="text-sm text-gray-500">30 minutes</div>
                    </div>
                </div>

                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="dance">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">6:30</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Traditional Garba Dance</h3>
                            <span class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">Dance</span>
                        </div>
                        <p class="text-gray-600 mb-2">Join the traditional Garba circles with authentic folk music and dance steps</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Dance Floor Area
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">6:30 - 8:00 PM</div>
                        <div class="text-sm text-gray-500">1.5 hours</div>
                    </div>
                </div>

                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="food">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">8:00</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Dinner Break</h3>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm font-semibold">Food</span>
                        </div>
                        <p class="text-gray-600 mb-2">Enjoy authentic Gujarati thali and traditional festive delicacies</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Food Court
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">8:00 - 9:00 PM</div>
                        <div class="text-sm text-gray-500">1 hour</div>
                    </div>
                </div>

                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="music">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">9:00</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Live Folk Music Performance</h3>
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm font-semibold">Music</span>
                            <span class="px-3 py-1 bg-yellow-100 text-yellow-600 rounded-full text-sm font-semibold">Featured</span>
                        </div>
                        <p class="text-gray-600 mb-2">Renowned folk artists perform traditional Gujarati songs and Garba music</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Main Stage
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">9:00 - 10:30 PM</div>
                        <div class="text-sm text-gray-500">1.5 hours</div>
                    </div>
                </div>

                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="competition">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">10:30</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Best Costume Competition</h3>
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm font-semibold">Competition</span>
                        </div>
                        <p class="text-gray-600 mb-2">Showcase your traditional attire and win exciting prizes</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Main Stage
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">10:30 - 11:00 PM</div>
                        <div class="text-sm text-gray-500">30 minutes</div>
                    </div>
                </div>

                <!-- Schedule Item -->
                <div class="schedule-item card p-6 flex flex-col md:flex-row items-start md:items-center gap-6" data-category="dance">
                    <div class="flex-shrink-0">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-red-500 rounded-full flex items-center justify-center">
                            <span class="text-white font-bold text-lg">11:00</span>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="text-xl font-bold text-gray-800">Grand Finale Garba</h3>
                            <span class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">Dance</span>
                            <span class="px-3 py-1 bg-yellow-100 text-yellow-600 rounded-full text-sm font-semibold">Featured</span>
                        </div>
                        <p class="text-gray-600 mb-2">Join everyone for the grand finale with energetic music and dance</p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            </svg>
                            Entire Venue
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-semibold text-gray-800">11:00 PM - 12:00 AM</div>
                        <div class="text-sm text-gray-500">1 hour</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Important Information -->
        <div class="animate-on-scroll">
            <div class="card p-8">
                <h3 class="text-2xl font-bold text-gradient mb-6">Important Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">What to Bring</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Traditional Garba attire (Chaniya Choli/Kurta)
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Dandiya sticks (available for purchase)
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Comfortable dancing shoes
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Water bottle and towel
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-3">Venue Facilities</h4>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Free parking available
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Clean restroom facilities
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                First aid station
                            </li>
                            <li class="flex items-center">
                                <svg class="w-4 h-4 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Photography and videography services
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    // Schedule filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.schedule-filter-btn');
        const scheduleItems = document.querySelectorAll('.schedule-item');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Filter schedule items
                scheduleItems.forEach(item => {
                    const category = item.getAttribute('data-category');
                    if (filter === 'all' || category === filter) {
                        item.style.display = 'flex';
                        item.classList.add('animate-on-scroll');
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    });
</script>

<style>
.schedule-filter-btn {
    @apply px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-red-50 hover:text-red-600 hover:border-red-300 transition-all duration-200 font-medium;
}

.schedule-filter-btn.active {
    @apply bg-red-500 text-white border-red-500;
}

.schedule-item {
    @apply transition-all duration-300;
}

.schedule-item:hover {
    @apply transform -translate-y-1 shadow-xl;
}
</style>
@endpush
@endsection
