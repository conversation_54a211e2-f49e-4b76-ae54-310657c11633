<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PollResponse extends Model
{
    use HasFactory;

    protected $fillable = [
        'poll_id',
        'user_id',
        'response_data',
        'ip_address',
        'user_agent',
    ];

    protected function casts(): array
    {
        return [
            'response_data' => 'array',
        ];
    }

    /**
     * Get the poll that owns the response
     */
    public function poll()
    {
        return $this->belongsTo(Poll::class);
    }

    /**
     * Get the user that owns the response
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
