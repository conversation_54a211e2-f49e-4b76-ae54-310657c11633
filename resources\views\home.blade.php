@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden garba-pattern">
    <!-- Background Video/Image -->
    <div class="absolute inset-0 z-0">
        <div class="hero-gradient absolute inset-0 z-10"></div>
        <div class="absolute inset-0 bg-black opacity-20 z-20"></div>
        <!-- You can replace this with a video background -->
        <div class="w-full h-full bg-cover bg-center bg-no-repeat festive-bg" style="background-image: url('{{ asset('images/garba/hero-bg.jpg') }}')"></div>
    </div>

    <!-- Animated Floating Elements -->
    <div class="absolute inset-0 z-25 pointer-events-none">
        <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400/20 rounded-full animate-float animate-logo-glow"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-red-400/20 rounded-full animate-float animate-sparkle" style="animation-delay: -1s;"></div>
        <div class="absolute bottom-32 left-20 w-24 h-24 bg-orange-400/20 rounded-full animate-float animate-dance" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-20 right-10 w-12 h-12 bg-blue-400/20 rounded-full animate-float animate-logo-bounce" style="animation-delay: -0.5s;"></div>
        <div class="absolute top-1/2 left-1/4 w-8 h-8 bg-green-400/20 rounded-full animate-sparkle" style="animation-delay: -3s;"></div>
        <div class="absolute top-1/3 right-1/3 w-14 h-14 bg-purple-400/20 rounded-full animate-dance" style="animation-delay: -1.5s;"></div>
        <div class="absolute top-3/4 left-1/2 w-10 h-10 bg-pink-400/20 rounded-full animate-logo-spin" style="animation-delay: -2.5s;"></div>

        <!-- Logo Elements -->
        <div class="absolute top-16 right-16">
            <img src="{{ asset('images/icons/rasgarba.png') }}" alt="Floating Logo"
                 class="w-16 h-16 object-contain animate-logo-spin opacity-20">
        </div>
        <div class="absolute bottom-16 left-16">
            <img src="{{ asset('images/icons/rasgarba.png') }}" alt="Floating Logo"
                 class="w-12 h-12 object-contain animate-logo-bounce opacity-30">
        </div>
    </div>

    <!-- Hero Content -->
    <div class="relative z-30 text-center text-white px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        <div class="animate-on-scroll">
            <!-- Main Logo -->
            <div class="mb-8">
                <img src="{{ asset('images/icons/rasgarba.png') }}" alt="RAS Garba Logo"
                     class="w-24 h-24 md:w-32 md:h-32 object-contain mx-auto animate-logo-glow mb-4">
            </div>

            <h1 class="font-display text-5xl md:text-7xl lg:text-8xl font-bold mb-6 animate-float text-glow">
                RAS Garba Event
            </h1>
            <p class="text-xl md:text-2xl lg:text-3xl font-medium mb-4 opacity-90 animate-slide-up">
                Experience the Magic of Traditional Garba
            </p>
            <p class="text-lg md:text-xl mb-8 opacity-80 max-w-3xl mx-auto animate-slide-up" style="animation-delay: 0.2s;">
                Join us for an unforgettable night of vibrant dance, soulful music, delicious food, and cultural celebration that brings our community together.
            </p>
            
            <!-- Event Details -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 mb-8 inline-block">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold">Oct 15, 2024</div>
                        <div class="text-sm opacity-80">Event Date</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">7:00 PM</div>
                        <div class="text-sm opacity-80">Start Time</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold">Garba Ground</div>
                        <div class="text-sm opacity-80">Venue</div>
                    </div>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up" style="animation-delay: 0.4s;">
                <a href="{{ route('book-pass') }}" class="btn-primary btn-large animate-pulse-glow interactive-hover group">
                    <svg class="w-5 h-5 mr-2 group-hover:animate-dance" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"></path>
                    </svg>
                    Book Your Pass Now
                    <span class="ml-2 group-hover:animate-bounce">🎫</span>
                </a>
                <a href="#about" class="btn-secondary btn-large interactive-hover group">
                    <svg class="w-5 h-5 mr-2 group-hover:animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Learn More
                    <span class="ml-2 group-hover:animate-bounce">✨</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
        <div class="animate-bounce">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section id="about" class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl md:text-5xl font-bold text-gradient mb-4">Why Choose RAS Garba?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Experience the authentic spirit of Garba with our carefully curated celebration that honors tradition while embracing modern joy.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Culture Card -->
            <div class="card-enhanced card-hover animate-on-scroll interactive-hover text-center p-8 group">
                <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-logo-bounce">
                    <svg class="w-8 h-8 text-white group-hover:animate-dance" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-red-600 transition-colors">Rich Culture 🎭</h3>
                <p class="text-gray-600">Immerse yourself in authentic Gujarati traditions with traditional costumes, rituals, and cultural performances.</p>
            </div>

            <!-- Music Card -->
            <div class="card-enhanced card-hover animate-on-scroll interactive-hover text-center p-8 group">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-logo-spin">
                    <svg class="w-8 h-8 text-white group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-4 group-hover:text-orange-600 transition-colors">Live Music 🎵</h3>
                <p class="text-gray-600">Dance to the rhythm of live traditional music and modern Garba beats performed by renowned artists.</p>
            </div>

            <!-- Food Card -->
            <div class="card card-hover animate-on-scroll text-center p-8">
                <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-4">Delicious Food</h3>
                <p class="text-gray-600">Savor authentic Gujarati cuisine and festive treats prepared by expert chefs using traditional recipes.</p>
            </div>

            <!-- Safety Card -->
            <div class="card card-hover animate-on-scroll text-center p-8">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-4">Safe Environment</h3>
                <p class="text-gray-600">Enjoy the celebration with complete peace of mind in our secure, well-organized, and family-friendly environment.</p>
            </div>
        </div>
    </div>
</section>

<!-- Event Information Section -->
<section class="py-20 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Content -->
            <div class="animate-on-scroll">
                <h2 class="text-4xl md:text-5xl font-bold text-gradient mb-6">
                    Join the Celebration
                </h2>
                <p class="text-xl text-gray-600 mb-8">
                    RAS Garba Event is more than just a festival – it's a celebration of community, culture, and joy. Experience the magic of traditional Garba dance, immerse yourself in vibrant colors, and create memories that will last a lifetime.
                </p>

                <!-- Event Stats -->
                <div class="grid grid-cols-3 gap-6 mb-8">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gradient counter" data-target="500">0</div>
                        <div class="text-sm text-gray-600">Expected Participants</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gradient counter" data-target="8">0</div>
                        <div class="text-sm text-gray-600">Hours of Celebration</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-gradient counter" data-target="15">0</div>
                        <div class="text-sm text-gray-600">Food Stalls</div>
                    </div>
                </div>

                <!-- Countdown Timer -->
                <div class="bg-white rounded-2xl p-6 shadow-lg mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 text-center">Event Starts In:</h3>
                    <div id="countdown" class="grid grid-cols-4 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-gradient" id="days">00</div>
                            <div class="text-xs text-gray-600">Days</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gradient" id="hours">00</div>
                            <div class="text-xs text-gray-600">Hours</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gradient" id="minutes">00</div>
                            <div class="text-xs text-gray-600">Minutes</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gradient" id="seconds">00</div>
                            <div class="text-xs text-gray-600">Seconds</div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('schedule') }}" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        View Schedule
                    </a>
                    <a href="{{ route('gallery') }}" class="btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        View Gallery
                    </a>
                </div>
            </div>

            <!-- Image/Map -->
            <div class="animate-on-scroll">
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
                    <!-- You can replace this with an actual map or venue image -->
                    <div class="h-64 bg-gradient-to-br from-red-200 to-orange-200 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <h3 class="text-xl font-bold text-gray-800">Garba Ground</h3>
                            <p class="text-gray-600">123 Festival Street, City</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Venue Details</h3>
                        <p class="text-gray-600 mb-4">
                            Our spacious venue offers ample parking, food courts, rest areas, and all modern amenities for a comfortable celebration.
                        </p>
                        <a href="{{ route('contact') }}" class="btn-outline btn-small">
                            Get Directions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
    // Countdown Timer
    function updateCountdown() {
        const eventDate = new Date('2024-10-15T19:00:00').getTime();
        const now = new Date().getTime();
        const distance = eventDate - now;

        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        } else {
            document.getElementById('countdown').innerHTML = '<div class="col-span-4 text-center text-2xl font-bold text-gradient">Event Started!</div>';
        }
    }

    // Update countdown every second
    updateCountdown();
    setInterval(updateCountdown, 1000);
</script>
@endpush
@endsection
