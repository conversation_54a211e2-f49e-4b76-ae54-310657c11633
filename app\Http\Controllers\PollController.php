<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Poll;
use App\Models\PollResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PollController extends Controller
{
    /**
     * Display the polls page
     */
    public function index()
    {
        $polls = Poll::where('is_active', true)
            ->with(['responses' => function($query) {
                $query->selectRaw('poll_id, option_value, COUNT(*) as vote_count')
                      ->groupBy('poll_id', 'option_value');
            }])
            ->orderBy('created_at', 'desc')
            ->get();

        // Calculate vote percentages for each poll
        foreach ($polls as $poll) {
            $totalVotes = $poll->responses->sum('vote_count');
            $poll->total_votes = $totalVotes;

            $poll->vote_percentages = [];
            foreach ($poll->responses as $response) {
                $percentage = $totalVotes > 0 ? round(($response->vote_count / $totalVotes) * 100) : 0;
                $poll->vote_percentages[$response->option_value] = [
                    'count' => $response->vote_count,
                    'percentage' => $percentage
                ];
            }
        }

        return view('pages.poll', compact('polls'));
    }

    /**
     * Submit a vote for a poll
     */
    public function vote(Request $request, $pollId)
    {
        $validator = Validator::make($request->all(), [
            'options' => 'required|array|min:1',
            'options.*' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please select at least one option.',
                'errors' => $validator->errors()
            ], 422);
        }

        $poll = Poll::where('id', $pollId)
            ->where('is_active', true)
            ->first();

        if (!$poll) {
            return response()->json([
                'success' => false,
                'message' => 'Poll not found or no longer active.'
            ], 404);
        }

        // Check if user has already voted
        $existingVote = PollResponse::where('poll_id', $pollId)
            ->where('user_id', Auth::id())
            ->first();

        if ($existingVote) {
            return response()->json([
                'success' => false,
                'message' => 'You have already voted in this poll.'
            ], 409);
        }

        try {
            // Store the vote(s)
            foreach ($request->options as $option) {
                PollResponse::create([
                    'poll_id' => $pollId,
                    'user_id' => Auth::id(),
                    'option_value' => $option,
                ]);
            }

            // Get updated vote counts
            $voteStats = $this->getPollStats($pollId);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for voting! Your voice has been heard.',
                'vote_stats' => $voteStats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your vote. Please try again.'
            ], 500);
        }
    }

    /**
     * Get poll statistics
     */
    private function getPollStats($pollId)
    {
        $responses = PollResponse::where('poll_id', $pollId)
            ->selectRaw('option_value, COUNT(*) as vote_count')
            ->groupBy('option_value')
            ->get();

        $totalVotes = $responses->sum('vote_count');
        $stats = [];

        foreach ($responses as $response) {
            $percentage = $totalVotes > 0 ? round(($response->vote_count / $totalVotes) * 100) : 0;
            $stats[$response->option_value] = [
                'count' => $response->vote_count,
                'percentage' => $percentage
            ];
        }

        return [
            'total_votes' => $totalVotes,
            'options' => $stats
        ];
    }

    /**
     * Create a new poll (admin functionality)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'options' => 'required|array|min:2|max:10',
            'options.*' => 'required|string|max:255',
            'poll_type' => 'required|in:single,multiple',
            'end_date' => 'required|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $poll = Poll::create([
                'title' => $request->title,
                'description' => $request->description,
                'options' => json_encode($request->options),
                'poll_type' => $request->poll_type,
                'end_date' => $request->end_date,
                'is_active' => true,
                'created_by' => Auth::id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Poll created successfully!',
                'poll' => $poll
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the poll. Please try again.'
            ], 500);
        }
    }

    /**
     * Get poll results (for admin or after voting)
     */
    public function results($pollId)
    {
        $poll = Poll::with(['responses' => function($query) {
            $query->selectRaw('poll_id, option_value, COUNT(*) as vote_count')
                  ->groupBy('poll_id', 'option_value');
        }])->find($pollId);

        if (!$poll) {
            return response()->json([
                'success' => false,
                'message' => 'Poll not found.'
            ], 404);
        }

        $totalVotes = $poll->responses->sum('vote_count');
        $results = [];

        foreach ($poll->responses as $response) {
            $percentage = $totalVotes > 0 ? round(($response->vote_count / $totalVotes) * 100, 1) : 0;
            $results[] = [
                'option' => $response->option_value,
                'votes' => $response->vote_count,
                'percentage' => $percentage
            ];
        }

        return response()->json([
            'success' => true,
            'poll' => $poll,
            'total_votes' => $totalVotes,
            'results' => $results
        ]);
    }
}
