<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'short_description',
        'tagline',
        'event_date',
        'start_time',
        'end_time',
        'venue',
        'venue_address',
        'venue_latitude',
        'venue_longitude',
        'featured_image',
        'gallery_images',
        'video_url',
        'ticket_price',
        'max_capacity',
        'current_bookings',
        'is_active',
        'is_featured',
        'organizers',
        'highlights',
    ];

    protected function casts(): array
    {
        return [
            'event_date' => 'date',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'gallery_images' => 'array',
            'organizers' => 'array',
            'highlights' => 'array',
            'ticket_price' => 'decimal:2',
            'venue_latitude' => 'decimal:8',
            'venue_longitude' => 'decimal:8',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ];
    }

    /**
     * Get the event's bookings
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the event's schedules
     */
    public function schedules()
    {
        return $this->hasMany(EventSchedule::class)->orderBy('schedule_date')->orderBy('start_time');
    }

    /**
     * Check if event has available spots
     */
    public function hasAvailableSpots(): bool
    {
        if (!$this->max_capacity) {
            return true;
        }

        return $this->current_bookings < $this->max_capacity;
    }

    /**
     * Get available spots count
     */
    public function getAvailableSpotsAttribute(): int
    {
        if (!$this->max_capacity) {
            return 999; // Unlimited
        }

        return max(0, $this->max_capacity - $this->current_bookings);
    }

    /**
     * Scope for active events
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured events
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for upcoming events
     */
    public function scopeUpcoming($query)
    {
        return $query->where('event_date', '>=', now()->toDateString());
    }
}
